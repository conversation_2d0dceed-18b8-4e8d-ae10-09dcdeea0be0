# 统一股票新闻服务

## 概述

统一股票新闻服务整合了热点新闻系统和专业股票新闻搜索，为股票分析提供全面、准确、实时的新闻信息。该服务通过智能聚合、相关性评估、去重合并等技术，为用户提供高质量的股票相关新闻。

## 核心特性

### 1. 多源新闻聚合
- **专业股票搜索**: 通过 Windmill + Gemini 进行精准的股票新闻搜索
- **热点新闻筛选**: 从热点新闻系统中筛选股票相关内容
- **统一数据格式**: 将不同来源的新闻转换为统一格式

### 2. 智能相关性评估
- **AI驱动评估**: 使用大语言模型评估新闻与特定股票的相关程度
- **多维度评分**: 综合考虑标题匹配、内容相关性、来源可靠性等因素
- **分级管理**: 将相关性分为 very_high、high、medium、low、very_low 五个级别

### 3. 高级去重和排序
- **智能去重**: 基于标题相似度和内容分析的智能去重算法
- **综合排序**: 结合相关性评分和时间因素的综合排序
- **质量优先**: 优先保留高相关性、高质量的新闻

### 4. 增强分析能力
- **情感分析**: 区分整体情感和高相关性新闻情感
- **影响评估**: 评估不同新闻对股价的潜在影响
- **趋势识别**: 识别新闻中的市场趋势和投资机会

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    统一股票新闻服务                          │
├─────────────────────────────────────────────────────────────┤
│                 StockNewsAggregator                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   专业搜索      │  │   热点新闻      │  │  AI相关性   │  │
│  │   NewsSearcher  │  │ HotNewsManager  │  │   评估      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
│           │                     │                   │       │
│           ▼                     ▼                   ▼       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              统一新闻格式转换和处理                      │ │
│  └─────────────────────────────────────────────────────────┘ │
│           │                                                 │
│           ▼                                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │         智能去重、排序和质量评估                         │ │
│  └─────────────────────────────────────────────────────────┘ │
│           │                                                 │
│           ▼                                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              综合分析和摘要生成                          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 主要组件

### StockNewsAggregator
统一股票新闻聚合器，核心组件。

**主要方法:**
- `get_comprehensive_stock_news()`: 获取综合股票新闻
- `get_stock_news_by_relevance()`: 根据相关性获取新闻
- `get_stock_news_summary_only()`: 仅获取新闻摘要

### WindmillClient (扩展)
扩展了 Windmill 客户端，支持批量相关性分析。

**新增方法:**
- `batch_analyze_stock_relevance()`: 批量分析新闻与股票的相关性

### HotNewsManager (扩展)
扩展了热点新闻管理器，支持股票专用搜索。

**新增方法:**
- `search_stock_related_news()`: 搜索股票相关热点新闻
- `get_relevant_news_for_stock()`: 获取高相关性股票新闻

### AnalysisEngine (增强)
增强了分析引擎，集成统一新闻服务。

**新增方法:**
- `generate_comprehensive_analysis_report()`: 生成使用统一新闻的分析报告

## 使用示例

### 基本用法

```python
from financial_analysis import StockNewsAggregator, StockInfo

# 初始化聚合器
aggregator = StockNewsAggregator()

# 创建股票信息
stock_info = StockInfo(
    symbol="000001", 
    name="平安银行", 
    exchange="SZSE", 
    sector="金融"
)

# 获取综合新闻
result = await aggregator.get_comprehensive_stock_news(
    stock_info=stock_info,
    days=7,
    include_hot_news=True,
    include_search_news=True
)

# 查看结果
print(f"总新闻数: {result['statistics']['total_count']}")
print(f"新闻摘要: {result['summary']}")

# 查看高相关性新闻
for news in result['news_items']:
    if news['relevance_score'] >= 80:
        print(f"标题: {news['title']}")
        print(f"相关性: {news['relevance_score']}")
        print(f"情感: {news['sentiment']}")
```

### 高相关性新闻筛选

```python
# 获取高相关性新闻
relevant_news = await aggregator.get_stock_news_by_relevance(
    stock_info=stock_info,
    min_relevance_score=70.0,
    limit=10
)

for news in relevant_news:
    print(f"{news['title']} - 相关性: {news['relevance_score']}")
```

### 增强分析报告

```python
from financial_analysis import AnalysisEngine

# 初始化分析引擎
engine = AnalysisEngine()

# 生成增强分析报告
report = await engine.generate_comprehensive_analysis_report(
    symbol="000001",
    exchange="SZSE"
)

print(f"AI分析: {report.ai_analysis}")
print(f"新闻摘要: {report.news_summary}")
```

## 配置说明

### 环境变量

```bash
# Windmill 配置（用于AI分析）
WINDMILL_BASE_URL=https://your-windmill-instance.com
WINDMILL_TOKEN=your-token
WINDMILL_WORKSPACE=your-workspace
WINDMILL_FOLDER=your-folder
WINDMILL_SCRIPT=your-script

# 新闻搜索配置
NEWS_SEARCH_DAYS=7
HOT_NEWS_ENABLED=true
HOT_NEWS_MAX_ITEMS=100
```

### 缓存配置

统一新闻服务使用多级缓存：
- **聚合结果缓存**: 30分钟
- **相关性评估缓存**: 1小时
- **新闻内容缓存**: 根据各组件配置

## 性能优化

### 1. 并行处理
- 专业搜索和热点新闻获取并行执行
- 批量相关性评估减少API调用次数

### 2. 智能缓存
- 多级缓存策略
- 基于股票代码和时间的缓存键
- 自动缓存失效机制

### 3. 数据去重
- 基于内容相似度的智能去重
- 保留高质量新闻
- 减少冗余信息

## 质量保证

### 1. 相关性评估
- AI驱动的多维度评估
- 0-100分的精确评分
- 五级相关性分类

### 2. 来源可靠性
- 区分专业搜索和热点新闻
- 来源类型标识
- 可靠性权重调整

### 3. 时效性保证
- 实时新闻获取
- 时间衰减算法
- 最新信息优先

## 错误处理

### 1. 降级策略
- Windmill 不可用时使用简化分析
- 单一数据源失败时继续其他源
- 缓存数据作为备选

### 2. 异常恢复
- 自动重试机制
- 错误日志记录
- 优雅降级处理

### 3. 数据验证
- 输入参数验证
- 数据格式检查
- 结果完整性验证

## 扩展性

### 1. 新数据源接入
- 标准化适配器接口
- 统一数据格式转换
- 动态数据源注册

### 2. 评估算法优化
- 可插拔的相关性算法
- 机器学习模型集成
- A/B测试支持

### 3. 分析能力扩展
- 自定义分析指标
- 多语言新闻支持
- 实时情感监控

## 监控和调试

### 1. 日志记录
- 详细的操作日志
- 性能指标记录
- 错误追踪信息

### 2. 性能监控
- 响应时间统计
- 缓存命中率
- API调用频率

### 3. 质量监控
- 相关性评分分布
- 新闻来源统计
- 用户反馈收集

## 最佳实践

### 1. 使用建议
- 合理设置相关性阈值
- 定期清理缓存
- 监控API配额使用

### 2. 性能优化
- 批量处理多个股票
- 合理设置缓存时间
- 避免频繁的全量刷新

### 3. 质量提升
- 定期评估相关性算法
- 收集用户反馈
- 持续优化数据源质量
