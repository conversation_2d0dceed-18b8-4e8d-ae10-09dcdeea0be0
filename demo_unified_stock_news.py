#!/usr/bin/env python3
"""
统一股票新闻服务演示

展示如何使用整合的热点新闻系统和专业股票新闻搜索，
提供统一的股票新闻服务功能。
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis import (
    StockNewsAggregator, 
    AnalysisEngine,
    StockInfo,
    HotNewsManager
)
from loguru import logger


def print_separator(title: str):
    """打印分隔符"""
    print("\n" + "=" * 60)
    print(f" {title} ")
    print("=" * 60)


async def demo_unified_stock_news():
    """演示统一股票新闻服务"""
    print_separator("统一股票新闻服务演示")
    
    # 初始化聚合器
    aggregator = StockNewsAggregator()
    
    # 测试股票信息
    test_stocks = [
        StockInfo(symbol="000001", name="平安银行", exchange="SZSE", sector="金融"),
        StockInfo(symbol="000002", name="万科A", exchange="SZSE", sector="房地产"),
        StockInfo(symbol="600036", name="招商银行", exchange="SSE", sector="金融")
    ]
    
    for stock_info in test_stocks:
        print(f"\n📊 分析股票: {stock_info.name} ({stock_info.symbol})")
        
        try:
            # 获取综合新闻
            result = await aggregator.get_comprehensive_stock_news(
                stock_info=stock_info,
                days=7,
                include_hot_news=True,
                include_search_news=True,
                max_news_per_source=10
            )
            
            # 显示统计信息
            stats = result.get('statistics', {})
            print(f"✅ 新闻获取成功:")
            print(f"   - 总新闻数: {stats.get('total_count', 0)}")
            print(f"   - 专业搜索: {stats.get('search_news_count', 0)} 条")
            print(f"   - 热点新闻: {stats.get('hot_news_count', 0)} 条")
            print(f"   - 处理时间: {stats.get('processing_time', 0)} 秒")
            
            # 显示新闻摘要
            summary = result.get('summary', '')
            if summary:
                print(f"\n📝 新闻摘要:")
                print(f"   {summary}")
            
            # 显示高相关性新闻
            news_items = result.get('news_items', [])
            high_relevance_news = []

            for news in news_items:
                # 处理字典格式的新闻数据
                if isinstance(news, dict):
                    relevance_score = news.get('relevance_score', 0)
                else:
                    # 处理对象格式的新闻数据
                    relevance_score = getattr(news, 'relevance_score', 0)

                if relevance_score >= 80:
                    high_relevance_news.append(news)

            if high_relevance_news:
                print(f"\n🔥 高相关性新闻 ({len(high_relevance_news)} 条):")
                for i, news in enumerate(high_relevance_news[:3], 1):
                    if isinstance(news, dict):
                        title = news.get('title', '')
                        source = news.get('source', '')
                        source_type = news.get('source_type', '')
                        relevance_score = news.get('relevance_score', 0)
                        sentiment = news.get('sentiment', 'neutral')
                    else:
                        title = getattr(news, 'title', '')
                        source = getattr(news, 'source', '')
                        source_type = getattr(news, 'source_type', '')
                        relevance_score = getattr(news, 'relevance_score', 0)
                        sentiment = getattr(news, 'sentiment', 'neutral')

                    print(f"   [{i}] {title}")
                    print(f"       来源: {source} ({source_type})")
                    print(f"       相关性: {relevance_score:.0f}分")
                    print(f"       情感: {sentiment}")
            
        except Exception as e:
            print(f"❌ 获取新闻失败: {str(e)}")


async def demo_stock_relevance_analysis():
    """演示股票相关性分析"""
    print_separator("股票相关性分析演示")
    
    aggregator = StockNewsAggregator()
    
    # 测试股票
    stock_info = StockInfo(symbol="000001", name="平安银行", exchange="SZSE", sector="金融")
    
    print(f"🔍 分析股票: {stock_info.name} ({stock_info.symbol})")
    
    try:
        # 获取高相关性新闻
        relevant_news = await aggregator.get_stock_news_by_relevance(
            stock_info=stock_info,
            min_relevance_score=70.0,
            limit=10
        )
        
        if relevant_news:
            print(f"✅ 找到 {len(relevant_news)} 条高相关性新闻:")
            
            for i, news in enumerate(relevant_news, 1):
                if isinstance(news, dict):
                    title = news.get('title', '')
                    relevance_score = news.get('relevance_score', 0)
                    relevance_level = news.get('relevance_level', 'unknown')
                    source = news.get('source', '')
                    source_type = news.get('source_type', '')
                    sentiment = news.get('sentiment', 'neutral')
                    category = news.get('category', 'unknown')
                else:
                    title = getattr(news, 'title', '')
                    relevance_score = getattr(news, 'relevance_score', 0)
                    relevance_level = getattr(news, 'relevance_level', 'unknown')
                    source = getattr(news, 'source', '')
                    source_type = getattr(news, 'source_type', '')
                    sentiment = getattr(news, 'sentiment', 'neutral')
                    category = getattr(news, 'category', 'unknown')

                print(f"\n[{i}] {title}")
                print(f"    相关性: {relevance_score:.0f}分")
                print(f"    级别: {relevance_level}")
                print(f"    来源: {source} ({source_type})")
                print(f"    情感: {sentiment}")
                print(f"    分类: {category}")
        else:
            print("❌ 未找到高相关性新闻")
            
    except Exception as e:
        print(f"❌ 相关性分析失败: {str(e)}")


async def demo_enhanced_analysis_report():
    """演示增强的分析报告"""
    print_separator("增强分析报告演示")
    
    # 初始化分析引擎
    engine = AnalysisEngine()
    
    # 测试股票
    test_symbol = "000001"
    
    print(f"📈 生成增强分析报告: {test_symbol}")
    
    try:
        # 生成综合分析报告
        report = await engine.generate_comprehensive_analysis_report(
            symbol=test_symbol,
            exchange="SZSE"
        )
        
        if report:
            print("✅ 增强分析报告生成成功:")
            print(f"   股票: {report.stock_info.name} ({report.stock_info.symbol})")
            print(f"   当前价格: {report.current_price:.2f}")
            print(f"   价格变化: {report.price_change:+.2f} ({report.price_change_percent:+.2f}%)")
            print(f"   综合评级: {report.overall_rating}")
            print(f"   风险等级: {report.risk_level}")
            
            print(f"\n📰 新闻分析:")
            print(f"   新闻数量: {len(report.news_items)}")
            print(f"   整体情感: {report.news_sentiment}")
            
            print(f"\n📝 新闻摘要:")
            print(f"   {report.news_summary}")
            
            print(f"\n🤖 AI分析:")
            ai_analysis = report.ai_analysis[:300] + "..." if len(report.ai_analysis) > 300 else report.ai_analysis
            print(f"   {ai_analysis}")
            
        else:
            print("❌ 分析报告生成失败")
            
    except Exception as e:
        print(f"❌ 生成分析报告失败: {str(e)}")


async def demo_hot_news_stock_search():
    """演示热点新闻的股票搜索功能"""
    print_separator("热点新闻股票搜索演示")
    
    # 初始化热点新闻管理器
    hot_news_manager = HotNewsManager()
    
    # 测试股票代码和名称
    test_cases = [
        ("000001", "平安银行"),
        ("600036", "招商银行"),
        ("000002", "万科A")
    ]
    
    for symbol, name in test_cases:
        print(f"\n🔍 搜索股票相关热点新闻: {name} ({symbol})")
        
        try:
            # 搜索股票相关新闻
            related_news = hot_news_manager.search_stock_related_news(
                stock_symbol=symbol,
                stock_name=name,
                limit=5
            )
            
            if related_news:
                print(f"✅ 找到 {len(related_news)} 条相关新闻:")
                
                for i, news in enumerate(related_news, 1):
                    relevance_score = getattr(news, 'stock_relevance_score', 0)
                    print(f"   [{i}] {news.title}")
                    print(f"       来源: {news.source}")
                    print(f"       相关性: {relevance_score:.1f}分")
                    print(f"       情感: {news.sentiment or 'unknown'}")
                    print(f"       重要性: {news.importance_level or 'unknown'}")
            else:
                print("❌ 未找到相关新闻")
                
        except Exception as e:
            print(f"❌ 搜索失败: {str(e)}")


async def demo_news_summary_only():
    """演示仅获取新闻摘要"""
    print_separator("新闻摘要服务演示")
    
    aggregator = StockNewsAggregator()
    
    # 测试股票
    test_stocks = [
        StockInfo(symbol="000001", name="平安银行", exchange="SZSE"),
        StockInfo(symbol="600519", name="贵州茅台", exchange="SSE")
    ]
    
    for stock_info in test_stocks:
        print(f"\n📄 获取新闻摘要: {stock_info.name} ({stock_info.symbol})")
        
        try:
            summary = await aggregator.get_stock_news_summary_only(stock_info)
            print(f"✅ 摘要: {summary}")
            
        except Exception as e:
            print(f"❌ 获取摘要失败: {str(e)}")


async def main():
    """主函数"""
    print("🚀 统一股票新闻服务演示程序")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 演示各种功能
        await demo_unified_stock_news()
        await demo_stock_relevance_analysis()
        await demo_hot_news_stock_search()
        await demo_news_summary_only()
        
        # 注意：增强分析报告需要Windmill配置，可能会失败
        print("\n⚠️  注意：以下演示需要Windmill配置，如果配置不完整可能会失败")
        await demo_enhanced_analysis_report()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n\n❌ 演示过程中发生错误: {str(e)}")
        logger.exception("演示程序异常")
    
    print(f"\n✅ 演示完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(sys.stderr, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    
    # 运行演示
    asyncio.run(main())
