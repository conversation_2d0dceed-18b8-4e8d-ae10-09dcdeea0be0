"""
Windmill 异步客户端模块

提供对 Windmill 工作流平台的异步调用功能，支持作业触发、状态轮询和结果获取。
基于 Windmill API 实现异步任务执行和结果等待机制。
"""

import asyncio
import json
import time
from typing import Optional, Dict, Any, Union
import aiohttp
from loguru import logger

from .config import settings
from .utils import format_error_message


class WindmillClient:
    """Windmill 异步客户端类"""
    
    def __init__(self, base_url: Optional[str] = None, token: Optional[str] = None, 
                 workspace: Optional[str] = None):
        """
        初始化 Windmill 客户端
        
        Args:
            base_url: Windmill 服务基础URL，默认从配置读取
            token: 访问令牌，默认从配置读取
            workspace: 工作空间名称，默认从配置读取
        """
        self.base_url = base_url or settings.windmill_base_url
        self.token = token or settings.windmill_token
        self.workspace = workspace or settings.windmill_workspace
        
        if not self.base_url or not self.token:
            logger.warning("Windmill 配置不完整，某些功能可能无法使用")
        
        # 默认请求头
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}" if self.token else ""
        }
        
        logger.info(f"Windmill 客户端初始化完成: {self.base_url}")
    
    async def trigger_job(self, folder: str, script: str, payload: Dict[str, Any] = None) -> Optional[str]:
        """
        触发 Windmill 作业
        
        Args:
            folder: 文件夹路径
            script: 脚本名称
            payload: 作业参数，默认为空字典
            
        Returns:
            作业UUID，如果触发失败返回None
        """
        if not self.base_url or not self.token:
            logger.error("Windmill 配置不完整，无法触发作业")
            return None
        
        if payload is None:
            payload = {}
        
        try:
            # 构建API端点URL
            endpoint = f"{self.base_url}/api/w/{self.workspace}/jobs/run/p/f/{folder}/{script}"
            
            logger.debug(f"触发 Windmill 作业: {endpoint}")
            logger.debug(f"作业参数: {json.dumps(payload, ensure_ascii=False)}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    endpoint,
                    headers=self.headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status not in [200, 201]:
                        logger.error(f"触发作业失败，状态码: {response.status}")
                        error_text = await response.text()
                        logger.error(f"错误响应: {error_text}")
                        return None
                    
                    # 获取作业UUID
                    job_uuid = await response.text()
                    job_uuid = job_uuid.strip().strip('"')  # 移除可能的引号
                    
                    logger.info(f"作业触发成功，UUID: {job_uuid}")
                    return job_uuid
                    
        except asyncio.TimeoutError:
            logger.error("触发作业超时")
            return None
        except Exception as e:
            logger.error(f"触发作业失败: {format_error_message(e, '触发Windmill作业')}")
            return None
    
    async def wait_for_job_completion(self, job_uuid: str, 
                                    max_wait_time: int = 300, 
                                    poll_interval: int = 1) -> Optional[Dict[str, Any]]:
        """
        等待作业完成并获取结果
        
        Args:
            job_uuid: 作业UUID
            max_wait_time: 最大等待时间（秒），默认5分钟
            poll_interval: 轮询间隔（秒），默认1秒
            
        Returns:
            作业完成结果，如果失败或超时返回None
        """
        if not self.base_url or not self.token:
            logger.error("Windmill 配置不完整，无法等待作业完成")
            return None
        
        try:
            # 构建结果查询端点URL
            endpoint = f"{self.base_url}/api/w/{self.workspace}/jobs_u/completed/get_result_maybe/{job_uuid}"
            
            logger.debug(f"等待作业完成: {job_uuid}")
            
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                while time.time() - start_time < max_wait_time:
                    try:
                        async with session.get(
                            endpoint,
                            headers=self.headers,
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as response:
                            
                            if response.status != 200:
                                logger.warning(f"查询作业状态失败，状态码: {response.status}")
                                await asyncio.sleep(poll_interval)
                                continue
                            
                            # 解析响应
                            result_data = await response.json()
                            
                            # 检查作业是否完成
                            if result_data.get('completed', False):
                                logger.info(f"作业完成: {job_uuid}")
                                return result_data
                            else:
                                # 作业未完成，继续等待
                                elapsed_time = time.time() - start_time
                                logger.debug(f"作业进行中，已等待 {elapsed_time:.1f} 秒")
                                await asyncio.sleep(poll_interval)
                                
                    except asyncio.TimeoutError:
                        logger.warning("查询作业状态超时，继续重试")
                        await asyncio.sleep(poll_interval)
                        continue
                    except Exception as e:
                        logger.warning(f"查询作业状态异常: {str(e)}，继续重试")
                        await asyncio.sleep(poll_interval)
                        continue
                
                # 超时
                logger.error(f"等待作业完成超时: {job_uuid} (超时时间: {max_wait_time}秒)")
                return None
                
        except Exception as e:
            logger.error(f"等待作业完成失败: {format_error_message(e, '等待Windmill作业完成')}")
            return None
    
    async def execute_job(self, folder: str, script: str, payload: Dict[str, Any] = None,
                         max_wait_time: int = 300, poll_interval: int = 1) -> Optional[Dict[str, Any]]:
        """
        执行完整的作业流程：触发作业 -> 等待完成 -> 获取结果
        
        Args:
            folder: 文件夹路径
            script: 脚本名称
            payload: 作业参数
            max_wait_time: 最大等待时间（秒）
            poll_interval: 轮询间隔（秒）
            
        Returns:
            作业执行结果，如果失败返回None
        """
        try:
            logger.info(f"开始执行 Windmill 作业: {folder}/{script}")
            
            # 1. 触发作业
            job_uuid = await self.trigger_job(folder, script, payload)
            if not job_uuid:
                logger.error("作业触发失败")
                return None
            
            # 2. 等待作业完成
            result = await self.wait_for_job_completion(job_uuid, max_wait_time, poll_interval)
            if not result:
                logger.error(f"作业执行失败或超时: {job_uuid}")
                return None
            
            logger.info(f"作业执行成功: {job_uuid}")
            return result
            
        except Exception as e:
            logger.error(f"执行作业失败: {format_error_message(e, '执行Windmill作业')}")
            return None
    
    async def generate_text_analysis(self, prompt: str, system_instruction: str = None,
                                   search: bool = False) -> Optional[str]:
        """
        使用 Windmill 生成文本分析

        Args:
            prompt: 分析提示词
            system_instruction: 系统指令
            search: 是否需要进行搜索，当需要进行搜索时设置为 True

        Returns:
            生成的分析文本，如果失败返回None
        """
        try:
            # 构建请求参数
            payload = {
                "prompt": prompt
            }
            
            if system_instruction:
                payload["system_instruction"] = system_instruction

            if search:
                payload["search"] = search
            
            # 执行作业
            result = await self.execute_job(
                folder=settings.windmill_folder,
                script=settings.windmill_script,
                payload=payload,
                max_wait_time=120  # 文本生成通常需要更长时间
            )
            
            if not result:
                return None
            
            # 提取分析结果 - 根据 Windmill + Gemini API 响应结构解析
            if 'result' in result:
                analysis_result = result['result']

                if isinstance(analysis_result, dict):
                    # 检查是否有错误
                    if 'error' in analysis_result:
                        error_info = analysis_result['error']
                        error_message = error_info.get('message', '未知错误')
                        logger.error(f"Windmill 作业执行失败: {error_message}")
                        return None

                    # 1. 优先检查 candidates 字段（标准 Gemini API 格式）
                    if 'candidates' in analysis_result and analysis_result['candidates']:
                        candidates = analysis_result['candidates']
                        if len(candidates) > 0:
                            candidate = candidates[0]  # 取第一个候选响应

                            # 提取 content.parts[0].text
                            if ('content' in candidate and
                                'parts' in candidate['content'] and
                                candidate['content']['parts']):
                                parts = candidate['content']['parts']
                                if len(parts) > 0 and 'text' in parts[0]:
                                    text_content = parts[0]['text']
                                    if text_content:  # 确保文本内容不为空
                                        return text_content

                    # 2. 检查 parsed 字段（可能包含结构化数据）
                    elif 'parsed' in analysis_result and analysis_result['parsed']:
                        parsed_data = analysis_result['parsed']
                        if isinstance(parsed_data, dict) and 'analysis' in parsed_data:
                            return parsed_data['analysis']

                    # 3. 检查是否有直接的 analysis 字段
                    elif 'analysis' in analysis_result:
                        return analysis_result['analysis']

                # 4. 如果结果是字符串，直接返回
                elif isinstance(analysis_result, str):
                    return analysis_result

            # 检查作业是否成功完成
            if result.get('success') is False:
                logger.warning("Windmill 作业执行失败")
                return None

            logger.warning(f"无法从结果中提取分析文本，结果格式: {type(result.get('result', 'N/A'))}")
            logger.debug(f"完整结果结构: {result}")
            return None
            
        except Exception as e:
            logger.error(f"生成文本分析失败: {format_error_message(e, '生成文本分析')}")
            return None

    async def batch_analyze_stock_relevance(self, news_items: list, stock_info: dict) -> Optional[list]:
        """
        批量分析新闻与股票的相关性

        Args:
            news_items: 新闻条目列表，每个条目包含 title, content, source 等字段
            stock_info: 股票信息，包含 symbol, name, sector 等字段

        Returns:
            分析结果列表，每个结果包含相关性评分和分类
        """
        try:
            # 构建批量分析提示词
            prompt = self._build_batch_relevance_prompt(news_items, stock_info)

            system_instruction = """你是一个专业的金融分析师，擅长分析新闻与特定股票的相关性。
请为每条新闻评估与指定股票的相关程度，并返回JSON格式的结果。
相关性评分范围：0-100，其中：
- 90-100: 直接相关（公司公告、财报、重大事件）
- 70-89: 高度相关（行业政策、竞争对手动态）
- 50-69: 中度相关（行业趋势、宏观经济）
- 30-49: 低度相关（间接影响）
- 0-29: 无关或微弱相关"""

            # 执行批量分析
            result = await self.execute_job(
                folder=settings.windmill_folder,
                script=settings.windmill_script,
                payload={
                    "prompt": prompt,
                    "system_instruction": system_instruction,
                    "search": False  # 相关性分析不需要搜索
                },
                max_wait_time=180  # 批量分析需要更长时间
            )

            if not result:
                return None

            # 解析批量分析结果
            return self._parse_batch_relevance_result(result)

        except Exception as e:
            logger.error(f"批量分析股票相关性失败: {format_error_message(e, '批量分析股票相关性')}")
            return None

    def _build_batch_relevance_prompt(self, news_items: list, stock_info: dict) -> str:
        """构建批量相关性分析提示词"""
        stock_symbol = stock_info.get('symbol', '')
        stock_name = stock_info.get('name', '')
        stock_sector = stock_info.get('sector', '')

        prompt = f"""请分析以下新闻与股票 {stock_name} ({stock_symbol}) 的相关性。

股票信息：
- 代码：{stock_symbol}
- 名称：{stock_name}
- 行业：{stock_sector}

新闻列表：
"""

        for i, news in enumerate(news_items[:20]):  # 限制最多20条新闻
            title = news.get('title', '')
            content = news.get('content', '')[:200] if news.get('content') else ''  # 限制内容长度
            source = news.get('source', '')

            prompt += f"""
[新闻 {i+1}]
标题：{title}
内容：{content}
来源：{source}
"""

        prompt += """
请为每条新闻返回JSON格式的分析结果：
{
  "results": [
    {
      "news_index": 1,
      "relevance_score": 85,
      "relevance_level": "high",
      "reason": "直接涉及该公司的财报发布",
      "category": "财报",
      "sentiment": "positive"
    }
  ]
}

要求：
1. 为每条新闻评估相关性评分（0-100）
2. 确定相关性级别：very_high(90-100), high(70-89), medium(50-69), low(30-49), very_low(0-29)
3. 简要说明相关性原因
4. 分类新闻类型：财报、公告、政策、行业、竞争、其他
5. 评估情感倾向：positive、negative、neutral
"""

        return prompt

    def _parse_batch_relevance_result(self, result: dict) -> Optional[list]:
        """解析批量相关性分析结果"""
        try:
            # 提取分析结果
            analysis_result = result.get('result')
            if not analysis_result:
                return None

            # 处理 Gemini API 响应格式
            if isinstance(analysis_result, dict):
                # 检查 candidates 字段
                if 'candidates' in analysis_result and analysis_result['candidates']:
                    candidate = analysis_result['candidates'][0]
                    if ('content' in candidate and
                        'parts' in candidate['content'] and
                        candidate['content']['parts']):
                        text_content = candidate['content']['parts'][0].get('text', '')

                        # 从文本中提取JSON
                        return self._extract_json_from_text(text_content)

                # 检查直接的结果字段
                elif 'results' in analysis_result:
                    return analysis_result['results']

            elif isinstance(analysis_result, str):
                # 尝试从字符串中提取JSON
                return self._extract_json_from_text(analysis_result)

            return None

        except Exception as e:
            logger.error(f"解析批量相关性结果失败: {str(e)}")
            return None

    def _extract_json_from_text(self, text: str) -> Optional[list]:
        """从文本中提取JSON数据"""
        try:
            import re

            # 尝试直接解析
            try:
                data = json.loads(text.strip())
                if isinstance(data, dict) and 'results' in data:
                    return data['results']
                elif isinstance(data, list):
                    return data
            except json.JSONDecodeError:
                pass

            # 尝试从代码块中提取
            json_pattern = r'```(?:json)?\s*\n(.*?)\n```'
            matches = re.findall(json_pattern, text, re.DOTALL | re.IGNORECASE)

            for match in matches:
                try:
                    data = json.loads(match.strip())
                    if isinstance(data, dict) and 'results' in data:
                        return data['results']
                    elif isinstance(data, list):
                        return data
                except json.JSONDecodeError:
                    continue

            # 尝试查找JSON对象
            json_obj_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            matches = re.findall(json_obj_pattern, text, re.DOTALL)

            for match in matches:
                try:
                    data = json.loads(match)
                    if isinstance(data, dict) and 'results' in data:
                        return data['results']
                except json.JSONDecodeError:
                    continue

            return None

        except Exception as e:
            logger.error(f"从文本提取JSON失败: {str(e)}")
            return None


# 全局客户端实例
windmill_client = WindmillClient()
