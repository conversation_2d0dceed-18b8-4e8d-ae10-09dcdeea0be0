"""
新闻搜索模块

通过Gemini大语言模型和Windmill部署的接口搜索股票相关新闻，
并进行情感分析和内容摘要。
"""

import json
import time
import asyncio
import re
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import requests
from loguru import logger

from .models import NewsItem, StockInfo
from .config import settings
from .utils import safe_json_parse
from .windmill_client import windmill_client



class NewsSearcher:
    """新闻搜索器类"""

    # 类级别的缓存，所有实例共享
    _global_cache = {}

    def __init__(self):
        """初始化新闻搜索器"""
        self._cache = NewsSearcher._global_cache  # 使用全局缓存
        self._cache_timeout = settings.data_cache_duration
        logger.info("新闻搜索器初始化完成")

    @classmethod
    def clear_global_cache(cls):
        """清除全局缓存"""
        cls._global_cache.clear()
        logger.info("新闻搜索器全局缓存已清除")
    
    def search_stock_news(self, stock_info: StockInfo, days: int = None) -> List[NewsItem]:
        """
        搜索股票相关新闻
        
        Args:
            stock_info: 股票基本信息
            days: 搜索天数，默认使用配置中的值
            
        Returns:
            新闻条目列表
        """
        try:
            days = days or settings.news_search_days
            cache_key = f"news_{stock_info.symbol}_{days}"
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                logger.debug(f"从缓存获取新闻数据: {stock_info.symbol}")
                return self._cache[cache_key]['data']
            
            logger.info(f"搜索股票新闻: {stock_info.name} ({stock_info.symbol}), 天数: {days}")
            
            # 构造搜索查询
            search_query = self._build_search_query(stock_info, days)
            
            # 调用Windmill接口进行搜索
            news_items = self._search_via_windmill(search_query, stock_info)
            
            # 缓存结果
            if news_items:
                self._cache[cache_key] = {
                    'data': news_items,
                    'timestamp': time.time()
                }
            
            return news_items
            
        except Exception as e:
            logger.error(f"搜索股票新闻失败 {stock_info.symbol}: {str(e)}")
            return []
    
    def analyze_news_sentiment(self, news_items: List[NewsItem]) -> Dict[str, Any]:
        """
        分析新闻情感倾向
        
        Args:
            news_items: 新闻条目列表
            
        Returns:
            情感分析结果，包含整体情感、正面/负面新闻数量等
        """
        try:
            if not news_items:
                return {
                    'overall_sentiment': 'neutral',
                    'positive_count': 0,
                    'negative_count': 0,
                    'neutral_count': 0,
                    'sentiment_score': 0.0
                }
            
            logger.info(f"分析 {len(news_items)} 条新闻的情感倾向")
            
            # 统计各种情感的新闻数量
            positive_count = sum(1 for item in news_items if item.sentiment == 'positive')
            negative_count = sum(1 for item in news_items if item.sentiment == 'negative')
            neutral_count = sum(1 for item in news_items if item.sentiment == 'neutral')
            
            # 计算情感得分 (正面+1, 负面-1, 中性0)
            sentiment_score = (positive_count - negative_count) / len(news_items)
            
            # 确定整体情感
            if sentiment_score > 0.2:
                overall_sentiment = 'positive'
            elif sentiment_score < -0.2:
                overall_sentiment = 'negative'
            else:
                overall_sentiment = 'neutral'
            
            return {
                'overall_sentiment': overall_sentiment,
                'positive_count': positive_count,
                'negative_count': negative_count,
                'neutral_count': neutral_count,
                'sentiment_score': sentiment_score
            }
            
        except Exception as e:
            logger.error(f"分析新闻情感失败: {str(e)}")
            return {
                'overall_sentiment': 'neutral',
                'positive_count': 0,
                'negative_count': 0,
                'neutral_count': 0,
                'sentiment_score': 0.0
            }
    
    def generate_news_summary(self, news_items: List[NewsItem], stock_info: StockInfo) -> str:
        """
        生成新闻摘要
        
        Args:
            news_items: 新闻条目列表
            stock_info: 股票基本信息
            
        Returns:
            新闻摘要文本
        """
        try:
            if not news_items:
                return f"未找到关于 {stock_info.name} ({stock_info.symbol}) 的相关新闻。"
            
            logger.info(f"生成新闻摘要: {stock_info.symbol}")
            
            # 准备新闻内容用于摘要
            news_content = []
            for item in news_items[:10]:  # 最多使用前10条新闻
                content = f"标题: {item.title}"
                if item.content:
                    content += f"\n内容: {item.content[:200]}..."  # 限制内容长度
                content += f"\n来源: {item.source}\n发布时间: {item.publish_time.strftime('%Y-%m-%d %H:%M')}\n"
                news_content.append(content)
            
            # 调用Windmill接口生成摘要
            summary = self._generate_summary_via_windmill(news_content, stock_info)
            
            return summary or f"关于 {stock_info.name} 的新闻摘要生成失败。"
            
        except Exception as e:
            logger.error(f"生成新闻摘要失败: {str(e)}")
            return f"关于 {stock_info.name} 的新闻摘要生成失败。"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache:
            return False

        cache_time = self._cache[cache_key]['timestamp']
        return time.time() - cache_time < self._cache_timeout

    def _build_search_query(self, stock_info: StockInfo, days: int) -> str:
        """构造搜索查询"""
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 构造查询字符串
        query_parts = [
            stock_info.name,
            stock_info.symbol,
        ]

        # 添加行业相关关键词
        if stock_info.sector:
            query_parts.append(stock_info.sector)

        # 添加时间限制
        date_range = f"{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}"

        query = f"搜索关于 {' '.join(query_parts)} 的最新新闻，时间范围：{date_range}"

        logger.debug(f"构造的搜索查询: {query}")
        return query

    def _search_via_windmill(self, search_query: str, stock_info: StockInfo) -> List[NewsItem]:
        """通过Windmill接口搜索新闻"""
        try:
            if not settings.windmill_base_url or not settings.windmill_token:
                logger.warning("Windmill配置不完整，使用模拟数据")
                return self._generate_mock_news(stock_info)

            # 使用异步客户端执行搜索 - 处理事件循环问题
            try:
                # 尝试获取当前事件循环
                loop = asyncio.get_running_loop()
                # 如果已经在事件循环中，创建新的线程来运行异步代码
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self._async_search_via_windmill(search_query, stock_info))
                    return future.result()
            except RuntimeError:
                # 没有运行中的事件循环，可以直接使用 asyncio.run
                return asyncio.run(self._async_search_via_windmill(search_query, stock_info))

        except Exception as e:
            logger.error(f"Windmill新闻搜索失败: {str(e)}")
            return self._generate_mock_news(stock_info)

    async def _async_search_via_windmill(self, search_query: str, stock_info: StockInfo) -> List[NewsItem]:
        """异步通过Windmill接口搜索新闻"""
        try:
            # 构建请求参数 - 移除 responseSchema，添加 search 参数
            payload = {
                "prompt": search_query,
                "system_instruction": "你是一个专业的金融新闻搜索助手。请搜索相关的股票新闻，并分析每条新闻的情感倾向（positive/negative/neutral）。返回JSON格式，包含news_items数组，每个条目包含title、content、source、publish_time、url、sentiment字段。",
                "search": True  # 启用搜索功能以获取最新新闻
            }

            logger.debug("开始异步调用Windmill新闻搜索API")

            # 使用异步客户端执行作业
            result = await windmill_client.execute_job(
                folder=settings.windmill_folder,
                script=settings.windmill_script,
                payload=payload,
                max_wait_time=120  # 新闻搜索可能需要更长时间
            )

            if not result:
                logger.warning("Windmill新闻搜索未返回结果")
                return []

            # 调试：输出API响应的基本结构信息
            logger.debug(f"Windmill API 响应结构: started={result.get('started')}, success={result.get('success')}, completed={result.get('completed')}")

            # 提取新闻数据 - 更新解析逻辑以适应新的响应格式
            news_data = []
            if 'result' in result:
                analysis_result = result['result']

                # 处理 Gemini API 的标准响应格式
                if isinstance(analysis_result, dict):
                    # 1. 检查 candidates 字段（标准 Gemini API 格式）
                    if 'candidates' in analysis_result and analysis_result['candidates']:
                        candidates = analysis_result['candidates']
                        if len(candidates) > 0:
                            candidate = candidates[0]

                            # 提取 content.parts 中的所有文本内容
                            if ('content' in candidate and
                                'parts' in candidate['content'] and
                                candidate['content']['parts']):
                                parts = candidate['content']['parts']

                                # 遍历所有parts，寻找包含JSON数据的部分
                                for part in parts:
                                    if 'text' in part:
                                        text_content = part['text']
                                        logger.debug(f"检查part文本内容: {text_content[:200]}...")

                                        # 尝试直接解析JSON
                                        json_data = self._extract_json_from_text(text_content)
                                        if json_data:
                                            if isinstance(json_data, dict) and 'news_items' in json_data:
                                                news_data = json_data['news_items']
                                                logger.info(f"成功从part中解析到 {len(news_data)} 条新闻数据")
                                                break
                                            elif isinstance(json_data, list):
                                                news_data = json_data
                                                logger.info(f"成功从part中解析到 {len(news_data)} 条新闻数据")
                                                break

                    # 2. 检查直接的 content.parts 格式（处理您提供的数据结构）
                    elif ('content' in analysis_result and
                          'parts' in analysis_result['content'] and
                          analysis_result['content']['parts']):
                        parts = analysis_result['content']['parts']

                        # 遍历所有parts，寻找包含JSON数据的部分
                        for part in parts:
                            if 'text' in part:
                                text_content = part['text']
                                logger.debug(f"检查content.parts文本内容: {text_content[:200]}...")

                                # 尝试提取JSON数据
                                json_data = self._extract_json_from_text(text_content)
                                if json_data:
                                    if isinstance(json_data, dict) and 'news_items' in json_data:
                                        news_data = json_data['news_items']
                                        logger.info(f"成功从content.parts中解析到 {len(news_data)} 条新闻数据")
                                        break
                                    elif isinstance(json_data, list):
                                        news_data = json_data
                                        logger.info(f"成功从content.parts中解析到 {len(news_data)} 条新闻数据")
                                        break

                    # 3. 检查直接的 news_items 字段（向后兼容）
                    elif 'news_items' in analysis_result:
                        news_data = analysis_result['news_items']

                    # 4. 检查是否整个结果就是新闻数组
                    elif isinstance(analysis_result, list):
                        news_data = analysis_result

                else:
                    logger.warning("Windmill返回的结果格式异常")
                    return []
            else:
                logger.warning("Windmill结果中缺少result字段")
                return []

            # 转换为NewsItem对象
            news_items = []
            for item in news_data:
                try:
                    # 使用智能时间解析函数
                    publish_time = self._parse_publish_time(item.get('publish_time', ''))

                    news_item = NewsItem(
                        title=item['title'],
                        content=item.get('content'),
                        source=item['source'],
                        publish_time=publish_time,
                        url=item.get('url'),
                        sentiment=item['sentiment']
                    )
                    news_items.append(news_item)
                    logger.debug(f"成功解析新闻条目: {item['title'][:50]}...")
                except Exception as e:
                    logger.warning(f"解析新闻条目失败: {str(e)}, 条目数据: {item}")
                    continue

            logger.info(f"通过Windmill异步获取到 {len(news_items)} 条新闻")
            return news_items

        except Exception as e:
            logger.error(f"异步Windmill新闻搜索失败: {str(e)}")
            return []

    def _extract_json_from_text(self, text_content: str) -> Optional[Dict[str, Any]]:
        """
        从文本内容中提取JSON数据

        支持以下格式：
        1. 纯JSON字符串
        2. Markdown代码块包装的JSON (```json ... ```)
        3. 包含其他文本的混合内容中的JSON

        Args:
            text_content: 包含JSON数据的文本内容

        Returns:
            解析后的JSON数据，如果解析失败返回None
        """
        try:
            import re

            # 1. 首先尝试直接解析整个文本作为JSON
            try:
                return json.loads(text_content.strip())
            except json.JSONDecodeError:
                pass

            # 2. 尝试从markdown代码块中提取JSON
            # 匹配 ```json ... ``` 或 ``` ... ``` 格式
            json_block_patterns = [
                r'```json\s*\n(.*?)\n```',  # ```json ... ```
                r'```\s*\n(.*?)\n```',      # ``` ... ```
                r'```json(.*?)```',         # ```json...``` (无换行)
                r'```(.*?)```'              # ```...``` (无换行)
            ]

            for pattern in json_block_patterns:
                matches = re.findall(pattern, text_content, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    try:
                        json_str = match.strip()
                        if json_str:
                            parsed_data = json.loads(json_str)
                            logger.debug(f"成功从markdown代码块中解析JSON数据")
                            return parsed_data
                    except json.JSONDecodeError:
                        continue

            # 3. 尝试查找文本中的JSON对象（以 { 开始，以 } 结束）
            json_object_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            matches = re.findall(json_object_pattern, text_content, re.DOTALL)
            for match in matches:
                try:
                    # 尝试解析找到的JSON对象
                    parsed_data = json.loads(match.strip())
                    if isinstance(parsed_data, dict):
                        logger.debug(f"成功从文本中提取JSON对象")
                        return parsed_data
                except json.JSONDecodeError:
                    continue

            # 4. 尝试查找更复杂的嵌套JSON结构
            # 使用更智能的括号匹配
            brace_count = 0
            start_pos = -1

            for i, char in enumerate(text_content):
                if char == '{':
                    if brace_count == 0:
                        start_pos = i
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0 and start_pos != -1:
                        # 找到完整的JSON对象
                        json_candidate = text_content[start_pos:i+1]
                        try:
                            parsed_data = json.loads(json_candidate)
                            if isinstance(parsed_data, dict):
                                logger.debug(f"成功通过括号匹配解析JSON数据")
                                return parsed_data
                        except json.JSONDecodeError:
                            pass
                        start_pos = -1

            logger.warning(f"无法从文本中提取有效的JSON数据: {text_content[:200]}...")
            return None

        except Exception as e:
            logger.error(f"提取JSON数据时发生错误: {str(e)}")
            return None

    def _parse_publish_time(self, time_str: str) -> datetime:
        """
        智能解析发布时间字符串

        支持多种时间格式：
        1. ISO格式：2025-07-25T09:20:00Z
        2. 标准格式：2025-07-25 09:20:00
        3. 带时区格式：2025-07-25 09:20 (美东)
        4. 简化格式：2025-07-25
        5. 相对时间：2小时前、1天前等

        Args:
            time_str: 时间字符串

        Returns:
            解析后的datetime对象，如果解析失败返回当前时间
        """
        try:
            if not time_str or not isinstance(time_str, str):
                logger.warning(f"无效的时间字符串: {time_str}")
                return datetime.now()

            time_str = time_str.strip()

            # 1. 尝试ISO格式解析
            try:
                # 处理带Z后缀的UTC时间
                if time_str.endswith('Z'):
                    return datetime.fromisoformat(time_str.replace('Z', '+00:00'))
                # 处理标准ISO格式
                return datetime.fromisoformat(time_str)
            except ValueError:
                pass

            # 2. 处理带时区标识的格式：2025-07-25 09:20 (美东)
            timezone_pattern = r'^(\d{4}-\d{2}-\d{2}(?:\s+\d{1,2}:\d{2}(?::\d{2})?)?)\s*\([^)]+\)$'
            match = re.match(timezone_pattern, time_str)
            if match:
                clean_time_str = match.group(1).strip()
                try:
                    # 尝试解析日期时间部分
                    if ':' in clean_time_str:
                        # 包含时间
                        if clean_time_str.count(':') == 1:
                            # 只有小时和分钟，添加秒
                            clean_time_str += ':00'
                        return datetime.strptime(clean_time_str, '%Y-%m-%d %H:%M:%S')
                    else:
                        # 只有日期
                        return datetime.strptime(clean_time_str, '%Y-%m-%d')
                except ValueError:
                    pass

            # 3. 处理标准格式：2025-07-25 09:20:00 或 2025-07-25 09:20
            if re.match(r'^\d{4}-\d{2}-\d{2}\s+\d{1,2}:\d{2}(:\d{2})?$', time_str):
                try:
                    if time_str.count(':') == 1:
                        # 只有小时和分钟
                        return datetime.strptime(time_str, '%Y-%m-%d %H:%M')
                    else:
                        # 包含秒
                        return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    pass

            # 4. 处理纯日期格式：2025-07-25
            if re.match(r'^\d{4}-\d{2}-\d{2}$', time_str):
                try:
                    return datetime.strptime(time_str, '%Y-%m-%d')
                except ValueError:
                    pass

            # 5. 处理相对时间格式（简化实现）
            relative_patterns = [
                (r'(\d+)\s*小时前', lambda m: datetime.now() - timedelta(hours=int(m.group(1)))),
                (r'(\d+)\s*天前', lambda m: datetime.now() - timedelta(days=int(m.group(1)))),
                (r'(\d+)\s*分钟前', lambda m: datetime.now() - timedelta(minutes=int(m.group(1)))),
                (r'刚刚|刚才', lambda m: datetime.now()),
                (r'昨天', lambda m: datetime.now() - timedelta(days=1)),
                (r'前天', lambda m: datetime.now() - timedelta(days=2)),
            ]

            for pattern, handler in relative_patterns:
                match = re.search(pattern, time_str)
                if match:
                    return handler(match)

            # 6. 如果所有解析都失败，记录警告并返回当前时间
            logger.warning(f"无法解析时间格式: '{time_str}'，使用当前时间")
            return datetime.now()

        except Exception as e:
            logger.error(f"解析时间字符串时发生错误: {str(e)}, 时间字符串: '{time_str}'")
            return datetime.now()

    def _generate_summary_via_windmill(self, news_content: List[str], stock_info: StockInfo) -> Optional[str]:
        """通过Windmill生成文本接口生成新闻摘要"""
        try:
            if not settings.windmill_base_url or not settings.windmill_token:
                logger.warning("Windmill配置不完整，返回简单摘要")
                return f"关于 {stock_info.name} 共找到 {len(news_content)} 条相关新闻。"

            # 使用异步客户端生成摘要 - 处理事件循环问题
            try:
                # 尝试获取当前事件循环
                loop = asyncio.get_running_loop()
                # 如果已经在事件循环中，创建新的线程来运行异步代码
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self._async_generate_summary_via_windmill(news_content, stock_info))
                    return future.result()
            except RuntimeError:
                # 没有运行中的事件循环，可以直接使用 asyncio.run
                return asyncio.run(self._async_generate_summary_via_windmill(news_content, stock_info))

        except Exception as e:
            logger.error(f"Windmill摘要生成失败: {str(e)}")
            return None

    async def _async_generate_summary_via_windmill(self, news_content: List[str], stock_info: StockInfo) -> Optional[str]:
        """异步通过Windmill生成文本接口生成新闻摘要"""
        try:
            # 准备新闻内容
            news_text = "\n\n".join(news_content)

            prompt = f"""
            请为以下关于 {stock_info.name} ({stock_info.symbol}) 的新闻内容生成一个简洁的摘要：

            {news_text}

            要求：
            1. 摘要应该突出重要的市场信息和趋势
            2. 长度控制在200字以内
            3. 使用中文
            4. 客观中性的语调
            5. 直接返回摘要文本，不需要JSON格式
            """

            # 构建请求参数 - 移除 responseSchema，添加 search 参数
            payload = {
                "prompt": prompt,
                "system_instruction": "你是一个专业的金融分析师，擅长总结和分析金融新闻。请直接返回摘要文本。",
                "search": False  # 摘要生成不需要搜索功能
            }

            logger.debug("开始异步调用Windmill摘要生成API")

            # 使用异步客户端执行作业
            result = await windmill_client.execute_job(
                folder=settings.windmill_folder,
                script=settings.windmill_script,
                payload=payload,
                max_wait_time=90  # 摘要生成通常较快
            )

            if not result:
                logger.warning("Windmill摘要生成未返回结果")
                return None

            # 提取摘要内容 - 更新解析逻辑以适应新的响应格式
            summary = None
            if 'result' in result:
                analysis_result = result['result']

                # 处理 Gemini API 的标准响应格式
                if isinstance(analysis_result, dict):
                    # 1. 检查 candidates 字段（标准 Gemini API 格式）
                    if 'candidates' in analysis_result and analysis_result['candidates']:
                        candidates = analysis_result['candidates']
                        if len(candidates) > 0:
                            candidate = candidates[0]

                            # 提取 content.parts[0].text
                            if ('content' in candidate and
                                'parts' in candidate['content'] and
                                candidate['content']['parts']):
                                parts = candidate['content']['parts']
                                if len(parts) > 0 and 'text' in parts[0]:
                                    summary = parts[0]['text']

                    # 2. 检查直接的 summary 字段（向后兼容）
                    elif 'summary' in analysis_result:
                        summary = analysis_result['summary']

                # 3. 如果结果直接是字符串
                elif isinstance(analysis_result, str):
                    summary = analysis_result
                else:
                    logger.warning("Windmill返回的摘要格式异常")
                    return None
            else:
                logger.warning("Windmill结果中缺少result字段")
                return None

            logger.info("新闻摘要异步生成成功")
            return summary

        except Exception as e:
            logger.error(f"异步Windmill摘要生成失败: {str(e)}")
            return None

    def _generate_mock_news(self, stock_info: StockInfo) -> List[NewsItem]:
        """生成模拟新闻数据（用于测试和降级）"""
        logger.info(f"生成模拟新闻数据: {stock_info.symbol}")

        mock_news = [
            NewsItem(
                title=f"{stock_info.name}发布最新财报，业绩超预期",
                content=f"{stock_info.name}公司发布了最新季度财报，营收和利润均超出市场预期。",
                source="财经新闻网",
                publish_time=datetime.now() - timedelta(hours=2),
                url="https://example.com/news1",
                sentiment="positive"
            ),
            NewsItem(
                title=f"市场分析师看好{stock_info.name}未来发展前景",
                content=f"多位分析师表示，{stock_info.name}在行业中的竞争优势明显。",
                source="投资者报",
                publish_time=datetime.now() - timedelta(hours=6),
                url="https://example.com/news2",
                sentiment="positive"
            ),
            NewsItem(
                title=f"{stock_info.name}面临行业监管政策调整压力",
                content=f"新的行业监管政策可能对{stock_info.name}的业务产生一定影响。",
                source="监管快讯",
                publish_time=datetime.now() - timedelta(hours=12),
                url="https://example.com/news3",
                sentiment="negative"
            )
        ]

        return mock_news
