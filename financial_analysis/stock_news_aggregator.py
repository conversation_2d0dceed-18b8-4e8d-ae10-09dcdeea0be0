"""
统一股票新闻聚合器模块

整合热点新闻系统和专业股票新闻搜索，提供统一的股票新闻服务。
支持多源新闻聚合、智能相关性评估、去重合并等功能。
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from loguru import logger

from .models import StockInfo, NewsItem, HotNewsItem, UnifiedStockNews
from .news_search import NewsSearcher
from .hot_news_manager import HotNewsManager
from .windmill_client import windmill_client
from .config import settings


class StockNewsAggregator:
    """统一股票新闻聚合器类"""
    
    def __init__(self):
        """初始化股票新闻聚合器"""
        self.news_searcher = NewsSearcher()
        self.hot_news_manager = HotNewsManager()
        self._cache = {}  # 聚合结果缓存
        self._cache_timeout = 1800  # 缓存30分钟
        logger.info("统一股票新闻聚合器初始化完成")
    
    async def get_comprehensive_stock_news(self, stock_info: StockInfo, 
                                         days: int = None,
                                         include_hot_news: bool = True,
                                         include_search_news: bool = True,
                                         max_news_per_source: int = 20) -> Dict[str, Any]:
        """
        获取股票的综合新闻信息
        
        Args:
            stock_info: 股票基本信息
            days: 搜索天数，默认使用配置值
            include_hot_news: 是否包含热点新闻
            include_search_news: 是否包含专业搜索新闻
            max_news_per_source: 每个来源的最大新闻数量
            
        Returns:
            综合新闻信息字典
        """
        try:
            cache_key = f"comprehensive_{stock_info.symbol}_{days}_{include_hot_news}_{include_search_news}"
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                logger.debug(f"从缓存获取综合新闻: {stock_info.symbol}")
                return self._cache[cache_key]['data']
            
            logger.info(f"获取股票综合新闻: {stock_info.name} ({stock_info.symbol})")
            start_time = time.time()
            
            # 并行获取不同来源的新闻
            tasks = []
            
            if include_search_news:
                tasks.append(self._get_search_news(stock_info, days, max_news_per_source))
            
            if include_hot_news:
                tasks.append(self._get_relevant_hot_news(stock_info, max_news_per_source))
            
            # 执行并行任务
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            search_news = []
            hot_news = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"获取新闻源 {i} 失败: {str(result)}")
                    continue
                
                if include_search_news and i == 0:
                    search_news = result or []
                elif include_hot_news:
                    hot_news = result or []
            
            # 转换为统一格式
            unified_news = await self._convert_to_unified_format(search_news, hot_news, stock_info)
            
            # 智能去重和排序
            deduplicated_news = self._intelligent_deduplication(unified_news)
            sorted_news = self._sort_by_relevance_and_time(deduplicated_news)
            
            # 生成综合摘要
            summary = await self._generate_comprehensive_summary(sorted_news, stock_info)
            
            # 统计信息
            processing_time = time.time() - start_time
            
            # 转换为字典格式以便序列化和访问
            news_items_dict = [news.dict() if hasattr(news, 'dict') else news for news in sorted_news]

            result = {
                "stock_info": stock_info.dict(),
                "news_items": news_items_dict,
                "summary": summary,
                "statistics": {
                    "total_count": len(sorted_news),
                    "search_news_count": len(search_news),
                    "hot_news_count": len(hot_news),
                    "processing_time": round(processing_time, 2)
                },
                "last_updated": datetime.now().isoformat()
            }
            
            # 缓存结果
            self._cache[cache_key] = {
                'data': result,
                'timestamp': time.time()
            }
            
            logger.info(f"获取综合新闻完成: {stock_info.symbol}, 共 {len(sorted_news)} 条")
            return result
            
        except Exception as e:
            logger.error(f"获取综合股票新闻失败: {str(e)}")
            return {
                "stock_info": stock_info.dict(),
                "news_items": [],
                "summary": f"获取 {stock_info.name} 新闻失败",
                "statistics": {"total_count": 0, "search_news_count": 0, "hot_news_count": 0},
                "error": str(e)
            }
    
    async def _get_search_news(self, stock_info: StockInfo, days: int, max_count: int) -> List[NewsItem]:
        """获取专业搜索新闻"""
        try:
            logger.debug(f"获取专业搜索新闻: {stock_info.symbol}")
            news_items = self.news_searcher.search_stock_news(stock_info, days)
            return news_items[:max_count] if news_items else []
        except Exception as e:
            logger.error(f"获取专业搜索新闻失败: {str(e)}")
            return []
    
    async def _get_relevant_hot_news(self, stock_info: StockInfo, max_count: int) -> List[HotNewsItem]:
        """获取相关热点新闻"""
        try:
            logger.debug(f"获取相关热点新闻: {stock_info.symbol}")
            
            # 构建搜索关键词
            keywords = [stock_info.symbol, stock_info.name]
            if stock_info.sector:
                keywords.append(stock_info.sector)
            
            # 搜索相关新闻
            all_relevant_news = []
            for keyword in keywords:
                relevant_news = self.hot_news_manager.search_news(keyword, limit=max_count)
                all_relevant_news.extend(relevant_news)
            
            # 去重并限制数量
            unique_news = self._deduplicate_hot_news(all_relevant_news)
            return unique_news[:max_count]
            
        except Exception as e:
            logger.error(f"获取相关热点新闻失败: {str(e)}")
            return []
    
    async def _convert_to_unified_format(self, search_news: List[NewsItem], 
                                       hot_news: List[HotNewsItem], 
                                       stock_info: StockInfo) -> List[UnifiedStockNews]:
        """转换为统一格式并评估相关性"""
        try:
            unified_news = []
            
            # 转换专业搜索新闻
            for news in search_news:
                unified_item = UnifiedStockNews(
                    id=f"search_{hash(news.title + str(news.publish_time))}",
                    title=news.title,
                    content=news.content,
                    source=news.source,
                    source_type="search",
                    url=news.url,
                    publish_time=news.publish_time,
                    sentiment=news.sentiment,
                    relevance_score=95.0,  # 专业搜索的相关性默认很高
                    relevance_level="very_high",
                    category="股票专题",
                    stock_symbol=stock_info.symbol,
                    stock_name=stock_info.name
                )
                unified_news.append(unified_item)
            
            # 转换热点新闻并评估相关性
            if hot_news:
                hot_news_data = [
                    {
                        "title": news.title,
                        "content": news.content or "",
                        "source": news.source
                    }
                    for news in hot_news
                ]
                
                # 批量评估相关性
                relevance_results = await windmill_client.batch_analyze_stock_relevance(
                    hot_news_data, 
                    stock_info.dict()
                )
                
                for i, news in enumerate(hot_news):
                    # 获取相关性评估结果
                    relevance_info = {}
                    if relevance_results and i < len(relevance_results):
                        relevance_info = relevance_results[i]
                    
                    unified_item = UnifiedStockNews(
                        id=f"hot_{news.news_id}",
                        title=news.title,
                        content=news.content,
                        source=news.source,
                        source_type="hot_news",
                        url=news.url,
                        publish_time=news.publish_time,
                        sentiment=relevance_info.get('sentiment', news.sentiment),
                        relevance_score=relevance_info.get('relevance_score', 50.0),
                        relevance_level=relevance_info.get('relevance_level', 'medium'),
                        category=relevance_info.get('category', news.category),
                        stock_symbol=stock_info.symbol,
                        stock_name=stock_info.name,
                        importance_level=news.importance_level,
                        heat_score=news.heat_score
                    )
                    unified_news.append(unified_item)
            
            return unified_news
            
        except Exception as e:
            logger.error(f"转换统一格式失败: {str(e)}")
            return []
    
    def _intelligent_deduplication(self, news_items: List[UnifiedStockNews]) -> List[UnifiedStockNews]:
        """智能去重"""
        try:
            if not news_items:
                return []
            
            # 按标题相似度去重
            unique_news = []
            seen_titles = set()
            
            for news in news_items:
                # 简化标题用于比较
                simplified_title = self._simplify_title(news.title)
                
                if simplified_title not in seen_titles:
                    seen_titles.add(simplified_title)
                    unique_news.append(news)
                else:
                    # 如果标题重复，保留相关性更高的
                    for i, existing_news in enumerate(unique_news):
                        if self._simplify_title(existing_news.title) == simplified_title:
                            if news.relevance_score > existing_news.relevance_score:
                                unique_news[i] = news
                            break
            
            logger.debug(f"去重前: {len(news_items)} 条，去重后: {len(unique_news)} 条")
            return unique_news
            
        except Exception as e:
            logger.error(f"智能去重失败: {str(e)}")
            return news_items
    
    def _simplify_title(self, title: str) -> str:
        """简化标题用于去重比较"""
        import re
        # 移除标点符号和空格，转为小写
        simplified = re.sub(r'[^\w\u4e00-\u9fff]', '', title.lower())
        return simplified
    
    def _sort_by_relevance_and_time(self, news_items: List[UnifiedStockNews]) -> List[UnifiedStockNews]:
        """按相关性和时间排序"""
        try:
            # 综合排序：相关性权重70%，时间权重30%
            def sort_key(news):
                # 时间评分：越新越高（最近7天内的新闻得分较高）
                time_diff = (datetime.now() - news.publish_time).total_seconds()
                time_score = max(0, 100 - (time_diff / (7 * 24 * 3600)) * 50)  # 7天内线性衰减
                
                # 综合评分
                combined_score = news.relevance_score * 0.7 + time_score * 0.3
                return combined_score
            
            sorted_news = sorted(news_items, key=sort_key, reverse=True)
            return sorted_news
            
        except Exception as e:
            logger.error(f"排序失败: {str(e)}")
            return news_items

    async def _generate_comprehensive_summary(self, news_items: List[UnifiedStockNews],
                                            stock_info: StockInfo) -> str:
        """生成综合新闻摘要"""
        try:
            if not news_items:
                return f"未找到关于 {stock_info.name} ({stock_info.symbol}) 的相关新闻。"

            # 选择最相关的新闻用于摘要
            top_news = [news for news in news_items if news.relevance_score >= 70][:10]

            if not top_news:
                top_news = news_items[:5]  # 如果没有高相关性新闻，取前5条

            # 构建摘要提示词
            prompt = f"""请为以下关于 {stock_info.name} ({stock_info.symbol}) 的新闻生成一个综合摘要：

股票信息：
- 代码：{stock_info.symbol}
- 名称：{stock_info.name}
- 行业：{stock_info.sector or '未知'}

相关新闻：
"""

            for i, news in enumerate(top_news, 1):
                prompt += f"""
[新闻 {i}] (相关性: {news.relevance_score:.0f}分)
标题：{news.title}
来源：{news.source}
情感：{news.sentiment}
"""
                if news.content:
                    prompt += f"内容摘要：{news.content[:150]}...\n"

            prompt += """
请生成一个200字以内的综合摘要，要求：
1. 突出与该股票最相关的重要信息
2. 总结市场情绪和趋势
3. 客观中性的语调
4. 使用中文
5. 直接返回摘要文本，不需要JSON格式
"""

            # 调用AI生成摘要
            summary = await windmill_client.generate_text_analysis(
                prompt=prompt,
                system_instruction="你是一个专业的金融分析师，擅长总结和分析股票相关新闻。",
                search=False
            )

            return summary or f"关于 {stock_info.name} 的新闻摘要生成失败。"

        except Exception as e:
            logger.error(f"生成综合摘要失败: {str(e)}")
            return f"关于 {stock_info.name} 的新闻摘要生成失败。"

    def _deduplicate_hot_news(self, news_items: List[HotNewsItem]) -> List[HotNewsItem]:
        """热点新闻去重"""
        try:
            seen_ids = set()
            unique_news = []

            for news in news_items:
                if news.news_id not in seen_ids:
                    seen_ids.add(news.news_id)
                    unique_news.append(news)

            return unique_news

        except Exception as e:
            logger.error(f"热点新闻去重失败: {str(e)}")
            return news_items

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache:
            return False

        cache_time = self._cache[cache_key]['timestamp']
        return time.time() - cache_time < self._cache_timeout

    def clear_cache(self):
        """清除缓存"""
        self._cache.clear()
        logger.info("股票新闻聚合器缓存已清除")

    async def get_stock_news_by_relevance(self, stock_info: StockInfo,
                                        min_relevance_score: float = 70.0,
                                        limit: int = 20) -> List[UnifiedStockNews]:
        """
        根据相关性获取股票新闻

        Args:
            stock_info: 股票信息
            min_relevance_score: 最小相关性评分
            limit: 返回数量限制

        Returns:
            高相关性新闻列表
        """
        try:
            # 获取综合新闻
            comprehensive_result = await self.get_comprehensive_stock_news(stock_info)
            all_news = comprehensive_result.get('news_items', [])

            # 过滤高相关性新闻
            relevant_news = [
                news for news in all_news
                if news.get('relevance_score', 0) >= min_relevance_score
            ]

            return relevant_news[:limit]

        except Exception as e:
            logger.error(f"获取高相关性新闻失败: {str(e)}")
            return []

    async def get_stock_news_summary_only(self, stock_info: StockInfo) -> str:
        """
        仅获取股票新闻摘要

        Args:
            stock_info: 股票信息

        Returns:
            新闻摘要文本
        """
        try:
            comprehensive_result = await self.get_comprehensive_stock_news(stock_info)
            return comprehensive_result.get('summary', f'无法获取 {stock_info.name} 的新闻摘要')

        except Exception as e:
            logger.error(f"获取新闻摘要失败: {str(e)}")
            return f'获取 {stock_info.name} 新闻摘要失败'


# 全局股票新闻聚合器实例
stock_news_aggregator = StockNewsAggregator()
