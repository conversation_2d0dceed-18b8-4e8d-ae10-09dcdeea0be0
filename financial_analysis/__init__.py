"""
金融证券分析项目

本项目旨在利用人工智能技术，特别是大语言模型，来辅助进行金融证券分析。
通过构建一个基于大语言模型的分析工具，实现自动化分析、智能化报告生成等功能。
"""

__version__ = "0.1.0"
__author__ = "AIER Team"
__email__ = "<EMAIL>"

from .config import settings
from .models import (
    StockInfo, AnalysisReport, StockPrice, TechnicalIndicators, NewsItem,
    HotNewsItem, HotNewsChannel, HotNewsCache, UnifiedStockNews
)
from .stock_data import StockDataProvider
from .news_search import NewsSearcher
from .analysis import AnalysisEngine
from .stock_news_aggregator import StockNewsAggregator, stock_news_aggregator
from .hot_news_manager import HotNewsManager, hot_news_manager
from .hot_news_collector import HotNewsCollector
from .hot_news_analyzer import HotNewsAnalyzer
from .hot_news_pusher import HotNewsPusher
from .hot_news_cache import HotNewsCacheManager, cache_manager

__all__ = [
    "settings",
    "StockInfo",
    "StockPrice",
    "TechnicalIndicators",
    "NewsItem",
    "AnalysisReport",
    "HotNewsItem",
    "HotNewsChannel",
    "HotNewsCache",
    "UnifiedStockNews",
    "StockDataProvider",
    "NewsSearcher",
    "AnalysisEngine",
    "StockNewsAggregator",
    "stock_news_aggregator",
    "HotNewsManager",
    "hot_news_manager",
    "HotNewsCollector",
    "HotNewsAnalyzer",
    "HotNewsPusher",
    "HotNewsCacheManager",
    "cache_manager",
]
