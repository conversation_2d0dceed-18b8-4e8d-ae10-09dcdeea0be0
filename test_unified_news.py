#!/usr/bin/env python3
"""
统一股票新闻服务测试脚本

快速测试统一新闻服务的核心功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis import StockNewsAggregator, StockInfo


async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试统一股票新闻服务基本功能")
    
    # 初始化聚合器
    aggregator = StockNewsAggregator()
    
    # 测试股票
    stock_info = StockInfo(
        symbol="000001", 
        name="平安银行", 
        exchange="SZSE", 
        sector="金融"
    )
    
    print(f"📊 测试股票: {stock_info.name} ({stock_info.symbol})")
    
    try:
        # 获取综合新闻
        result = await aggregator.get_comprehensive_stock_news(
            stock_info=stock_info,
            days=3,  # 减少天数以加快测试
            include_hot_news=True,
            include_search_news=True,
            max_news_per_source=5  # 减少数量以加快测试
        )
        
        # 验证结果结构
        assert 'news_items' in result, "结果中缺少 news_items"
        assert 'summary' in result, "结果中缺少 summary"
        assert 'statistics' in result, "结果中缺少 statistics"
        
        stats = result['statistics']
        news_items = result['news_items']
        
        print("✅ 基本功能测试通过:")
        print(f"   - 总新闻数: {stats.get('total_count', 0)}")
        print(f"   - 专业搜索: {stats.get('search_news_count', 0)} 条")
        print(f"   - 热点新闻: {stats.get('hot_news_count', 0)} 条")
        print(f"   - 处理时间: {stats.get('processing_time', 0)} 秒")
        
        # 验证新闻数据格式
        if news_items:
            first_news = news_items[0]
            required_fields = ['title', 'source', 'relevance_score', 'source_type']
            
            for field in required_fields:
                assert field in first_news, f"新闻数据缺少字段: {field}"
            
            print(f"   - 数据格式验证通过")
            print(f"   - 示例新闻: {first_news.get('title', '')[:50]}...")
        
        # 测试摘要功能
        summary = result.get('summary', '')
        if summary:
            print(f"   - 摘要生成成功: {len(summary)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {str(e)}")
        return False


async def test_relevance_filtering():
    """测试相关性过滤功能"""
    print("\n🔍 测试相关性过滤功能")
    
    aggregator = StockNewsAggregator()
    stock_info = StockInfo(symbol="000001", name="平安银行", exchange="SZSE")
    
    try:
        # 测试高相关性新闻筛选
        relevant_news = await aggregator.get_stock_news_by_relevance(
            stock_info=stock_info,
            min_relevance_score=70.0,
            limit=5
        )
        
        print(f"✅ 相关性过滤测试通过:")
        print(f"   - 高相关性新闻数量: {len(relevant_news)}")
        
        # 验证相关性评分
        for news in relevant_news:
            relevance_score = news.get('relevance_score', 0)
            assert relevance_score >= 70.0, f"相关性评分不符合要求: {relevance_score}"
        
        if relevant_news:
            print(f"   - 最高相关性: {max(news.get('relevance_score', 0) for news in relevant_news):.1f}分")
        
        return True
        
    except Exception as e:
        print(f"❌ 相关性过滤测试失败: {str(e)}")
        return False


async def test_summary_only():
    """测试仅摘要功能"""
    print("\n📄 测试仅摘要功能")
    
    aggregator = StockNewsAggregator()
    stock_info = StockInfo(symbol="000001", name="平安银行", exchange="SZSE")
    
    try:
        summary = await aggregator.get_stock_news_summary_only(stock_info)
        
        assert isinstance(summary, str), "摘要应该是字符串类型"
        assert len(summary) > 0, "摘要不能为空"
        
        print(f"✅ 仅摘要功能测试通过:")
        print(f"   - 摘要长度: {len(summary)} 字符")
        print(f"   - 摘要预览: {summary[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 仅摘要功能测试失败: {str(e)}")
        return False


async def test_cache_functionality():
    """测试缓存功能"""
    print("\n💾 测试缓存功能")
    
    aggregator = StockNewsAggregator()
    stock_info = StockInfo(symbol="000001", name="平安银行", exchange="SZSE")
    
    try:
        # 第一次调用
        start_time = asyncio.get_event_loop().time()
        result1 = await aggregator.get_comprehensive_stock_news(
            stock_info=stock_info,
            days=3,
            max_news_per_source=3
        )
        first_call_time = asyncio.get_event_loop().time() - start_time
        
        # 第二次调用（应该使用缓存）
        start_time = asyncio.get_event_loop().time()
        result2 = await aggregator.get_comprehensive_stock_news(
            stock_info=stock_info,
            days=3,
            max_news_per_source=3
        )
        second_call_time = asyncio.get_event_loop().time() - start_time
        
        # 验证缓存效果
        assert result1['statistics']['total_count'] == result2['statistics']['total_count'], "缓存结果不一致"
        
        print(f"✅ 缓存功能测试通过:")
        print(f"   - 第一次调用: {first_call_time:.2f} 秒")
        print(f"   - 第二次调用: {second_call_time:.2f} 秒")
        print(f"   - 缓存加速: {(first_call_time / second_call_time):.1f}x")
        
        # 清除缓存
        aggregator.clear_cache()
        print(f"   - 缓存已清除")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存功能测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 统一股票新闻服务测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(await test_basic_functionality())
    test_results.append(await test_relevance_filtering())
    test_results.append(await test_summary_only())
    test_results.append(await test_cache_functionality())
    
    # 汇总结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果汇总:")
    print(f"   - 通过测试: {passed_tests}/{total_tests}")
    print(f"   - 成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！统一股票新闻服务工作正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return 1


if __name__ == "__main__":
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
