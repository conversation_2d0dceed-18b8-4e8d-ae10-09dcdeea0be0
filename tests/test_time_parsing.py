"""
测试新闻时间解析功能

验证NewsSearcher._parse_publish_time方法能够正确处理各种时间格式
"""

import unittest
from datetime import datetime, timedelta
from financial_analysis.news_search import NewsSearcher


class TestTimeParsingFunctionality(unittest.TestCase):
    """测试时间解析功能"""
    
    def setUp(self):
        """设置测试环境"""
        self.searcher = NewsSearcher()
    
    def test_iso_format_parsing(self):
        """测试ISO格式时间解析"""
        test_cases = [
            ('2025-07-25T09:20:00Z', '2025-07-25 09:20:00+00:00'),
            ('2025-07-25T09:20:00', '2025-07-25 09:20:00'),
            ('2025-07-25', '2025-07-25 00:00:00'),
        ]
        
        for input_time, expected_str in test_cases:
            with self.subTest(input_time=input_time):
                result = self.searcher._parse_publish_time(input_time)
                self.assertIsInstance(result, datetime)
                # 对于带时区的时间，检查时间部分
                if '+00:00' in expected_str:
                    self.assertEqual(result.strftime('%Y-%m-%d %H:%M:%S'), expected_str.replace('+00:00', ''))
                else:
                    self.assertEqual(result.strftime('%Y-%m-%d %H:%M:%S'), expected_str)
    
    def test_timezone_format_parsing(self):
        """测试带时区标识的时间格式解析"""
        test_cases = [
            '2025-07-25 09:20 (美东)',
            '2025-07-25 09:20:30 (UTC)',
            '2025-07-25 14:30 (北京时间)',
            '2025-07-25 (全天)',
        ]
        
        for input_time in test_cases:
            with self.subTest(input_time=input_time):
                result = self.searcher._parse_publish_time(input_time)
                self.assertIsInstance(result, datetime)
                # 验证日期部分正确
                self.assertEqual(result.strftime('%Y-%m-%d'), '2025-07-25')
    
    def test_standard_format_parsing(self):
        """测试标准格式时间解析"""
        test_cases = [
            ('2025-07-25 09:20:00', '2025-07-25 09:20:00'),
            ('2025-07-25 09:20', '2025-07-25 09:20:00'),
            ('2025-07-25', '2025-07-25 00:00:00'),
        ]
        
        for input_time, expected in test_cases:
            with self.subTest(input_time=input_time):
                result = self.searcher._parse_publish_time(input_time)
                self.assertIsInstance(result, datetime)
                self.assertEqual(result.strftime('%Y-%m-%d %H:%M:%S'), expected)
    
    def test_relative_time_parsing(self):
        """测试相对时间格式解析"""
        now = datetime.now()
        
        test_cases = [
            ('2小时前', timedelta(hours=2)),
            ('1天前', timedelta(days=1)),
            ('30分钟前', timedelta(minutes=30)),
            ('昨天', timedelta(days=1)),
            ('前天', timedelta(days=2)),
        ]
        
        for input_time, expected_delta in test_cases:
            with self.subTest(input_time=input_time):
                result = self.searcher._parse_publish_time(input_time)
                self.assertIsInstance(result, datetime)
                
                # 验证时间差在合理范围内（允许几秒的误差）
                expected_time = now - expected_delta
                time_diff = abs((result - expected_time).total_seconds())
                self.assertLess(time_diff, 10, f"时间差过大: {time_diff}秒")
    
    def test_invalid_format_handling(self):
        """测试无效格式的处理"""
        invalid_inputs = [
            '',
            None,
            'invalid-date',
            '2025-13-45',  # 无效日期
            '25:99:99',    # 无效时间
            'random text',
        ]
        
        for invalid_input in invalid_inputs:
            with self.subTest(invalid_input=invalid_input):
                result = self.searcher._parse_publish_time(invalid_input)
                self.assertIsInstance(result, datetime)
                # 无效输入应该返回当前时间（允许几秒误差）
                time_diff = abs((result - datetime.now()).total_seconds())
                self.assertLess(time_diff, 10)
    
    def test_edge_cases(self):
        """测试边界情况"""
        edge_cases = [
            '2025-07-25 09:20 (美东时间)',  # 更长的时区描述
            '2025-07-25 9:20',             # 单位数小时
            '2025-7-25',                   # 单位数月日
            '刚刚',                        # 特殊相对时间
            '刚才',                        # 特殊相对时间
        ]
        
        for input_time in edge_cases:
            with self.subTest(input_time=input_time):
                result = self.searcher._parse_publish_time(input_time)
                self.assertIsInstance(result, datetime)
    
    def test_news_item_creation_with_various_time_formats(self):
        """测试使用各种时间格式创建新闻条目"""
        from financial_analysis.models import NewsItem
        
        # 模拟新闻数据
        news_data = [
            {
                'title': '测试新闻1',
                'content': '测试内容1',
                'source': '测试来源1',
                'publish_time': '2025-07-25 09:20 (美东)',
                'url': 'https://example.com/1',
                'sentiment': 'positive'
            },
            {
                'title': '测试新闻2',
                'content': '测试内容2',
                'source': '测试来源2',
                'publish_time': '2025-07-25T09:20:00Z',
                'url': 'https://example.com/2',
                'sentiment': 'neutral'
            },
            {
                'title': '测试新闻3',
                'content': '测试内容3',
                'source': '测试来源3',
                'publish_time': '2小时前',
                'url': 'https://example.com/3',
                'sentiment': 'negative'
            }
        ]
        
        # 模拟新闻条目创建过程
        news_items = []
        for item in news_data:
            try:
                publish_time = self.searcher._parse_publish_time(item.get('publish_time', ''))
                
                news_item = NewsItem(
                    title=item['title'],
                    content=item.get('content'),
                    source=item['source'],
                    publish_time=publish_time,
                    url=item.get('url'),
                    sentiment=item['sentiment']
                )
                news_items.append(news_item)
            except Exception as e:
                self.fail(f"创建新闻条目失败: {str(e)}, 数据: {item}")
        
        # 验证所有新闻条目都成功创建
        self.assertEqual(len(news_items), 3)
        
        # 验证时间解析正确
        for news_item in news_items:
            self.assertIsInstance(news_item.publish_time, datetime)


if __name__ == '__main__':
    unittest.main()
